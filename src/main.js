import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { Button, Field, Form, Icon, Search, Tabbar, TabbarItem, Popup, Dialog, Picker, Tabs, Tab, DatePicker, Toast } from 'vant'
import 'vant/lib/index.css'
import './style.css'
import App from './App.vue'
import Login from './views/Login.vue'
import Home from './views/Home.vue'
import Profile from './views/Profile.vue'

import api from './utils/api'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Login',
    component: Login
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isLoggedIn = api.isLoggedIn()

  console.log('路由守卫:', {
    to: to.path,
    from: from.path,
    isLoggedIn
  })

  // 如果访问登录页
  if (to.path === '/') {
    if (isLoggedIn) {
      // 已登录，跳转到首页
      next('/home')
    } else {
      // 未登录，正常访问登录页
      next()
    }
  }
  // 如果访问需要登录的页面
  else if (to.path === '/home' || to.path === '/profile') {
    if (isLoggedIn) {
      // 已登录，正常访问
      next()
    } else {
      // 未登录，跳转到登录页
      console.log('未登录，跳转到登录页')
      next('/')
    }
  }
  // 其他页面
  else {
    next()
  }
})

const app = createApp(App)

// 注册Vant组件
app.use(Button)
app.use(Field)
app.use(Form)
app.use(Icon)
app.use(Search)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Popup)
app.use(Dialog)
app.use(Picker)
app.use(Tabs)
app.use(Tab)
app.use(DatePicker)
app.use(Toast)
app.use(router)

app.mount('#app')

// 开发环境下导入测试工具
if (import.meta.env.DEV) {
  import('./utils/auth-test.js')
}
