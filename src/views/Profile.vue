<template>
  <div class="profile-container">
    <!-- 账号信息区域 -->
    <div class="account-section">
      <div class="account-info">
        <div class="avatar-container">
          <img 
            :src="userInfo.avatar" 
            :alt="userInfo.name"
            class="user-avatar"
          />
          <div class="online-status"></div>
        </div>
        <div class="user-details">
          <h3 class="user-name">{{ userInfo.name }}</h3>
          <p class="user-group">{{ userInfo.group }}</p>
          <p class="user-role">{{ userInfo.role }}</p>
        </div>
        <div class="logout-btn" @click="showLogoutDialog">
          <van-icon name="arrow-left" color="white" size="16" />
          <span class="logout-text">Logout</span>
        </div>
      </div>
    </div>

    <!-- Tab选项区域 -->
    <div class="tabs-section">
      <van-tabs 
        v-model:active="activeTab" 
        class="custom-tabs"
        color="#7ed321"
        title-active-color="#7ed321"
        title-inactive-color="#666666"
      >
        <van-tab title="Order History" name="orders">
          <!-- 订单记录内容 -->
          <div class="orders-content">
            <div class="orders-header">
              <h4 class="orders-title">Recent Orders</h4>
              <div class="header-actions">
                <button
                  v-if="hasActiveFilters"
                  class="reset-filters-btn"
                  @click="resetFilters"
                >
                  Reset
                </button>
                <div class="filter-btn" @click="openFilterPopup">
                  <van-icon name="filter-o" color="#7ed321" size="16" />
                  <span>Filter</span>
                  <div v-if="hasActiveFilters" class="filter-badge"></div>
                </div>
              </div>
            </div>
            
            <!-- 订单列表 -->
            <div class="orders-list">
              <div
                v-for="order in ordersList"
                :key="order.id"
                class="order-item"
                @click="handleOrderClick(order)"
              >
                <div class="order-header">
                  <div class="order-info">
                    <span class="order-number">#{{ order.orderNumber }}</span>
                    <span class="order-date">{{ order.date }}</span>
                  </div>
                  <div class="order-status" :class="order.status.toLowerCase()">
                    {{ order.status }}
                  </div>
                </div>
                
                <div class="order-content">
                  <div class="order-items">
                    <div 
                      v-for="item in order.items.slice(0, 2)" 
                      :key="item.id"
                      class="order-product"
                    >
                      <img :src="item.image" :alt="item.name" class="product-thumb" />
                      <div class="product-info">
                        <span class="product-name">{{ item.name }}</span>
                        <span class="product-quantity">x{{ item.quantity }}</span>
                      </div>
                    </div>
                    <div v-if="order.items.length > 2" class="more-items">
                      +{{ order.items.length - 2 }} more items
                    </div>
                  </div>
                  
                  <div class="order-footer">
                    <div class="order-total">
                      <span class="total-amount">${{ order.total.toFixed(2) }}</span>
                    </div>
                    <div class="order-actions">
                      <button class="action-btn view-btn">View</button>
                      <button 
                        v-if="order.status === 'Delivered'"
                        class="action-btn reorder-btn"
                      >
                        Reorder
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-tab>
        
        <van-tab title="Statistics" name="stats">
          <div class="stats-content">
            <div class="stats-header">
              <van-icon name="chart-trending-o" color="#7ed321" size="24" />
              <h3 class="stats-title">Sales Performance</h3>
            </div>

            <!-- 时间选择器 -->
            <div class="date-selector">
              <div class="date-inputs">
                <div class="date-input-group">
                  <label class="date-label">Start Date</label>
                  <van-field
                    v-model="statsForm.startDate"
                    readonly
                    placeholder="Select start date"
                    @click="showStartDatePicker = true"
                    :border="false"
                    class="date-field"
                  >
                    <template #right-icon>
                      <van-icon name="calendar-o" color="#7ed321" size="16" />
                    </template>
                  </van-field>
                </div>

                <div class="date-input-group">
                  <label class="date-label">End Date</label>
                  <van-field
                    v-model="statsForm.endDate"
                    readonly
                    placeholder="Select end date"
                    @click="showEndDatePicker = true"
                    :border="false"
                    class="date-field"
                  >
                    <template #right-icon>
                      <van-icon name="calendar-o" color="#7ed321" size="16" />
                    </template>
                  </van-field>
                </div>
              </div>

              <!-- 快速选择选项 - 横向布局 -->
              <div class="stats-date-range-quick-options">
                <div
                  v-for="range in quickDateRangeOptions"
                  :key="range.value"
                  class="stats-quick-date-option"
                  @click="selectStatsDateRange(range.value)"
                >
                  <span>{{ range.label }}</span>
                </div>

                <!-- 清除时间选项 -->
                <div
                  class="stats-quick-date-option stats-clear-date-option"
                  @click="clearStatsDateRange"
                >
                  <van-icon name="delete-o" color="#e74c3c" size="12" />
                  <span>Clear</span>
                </div>
              </div>

              <button
                class="query-btn"
                :disabled="!isDateRangeValid || isLoadingStats"
                @click="queryStatistics"
              >
                <van-loading v-if="isLoadingStats" size="16px" color="white" />
                <span v-else>Query Statistics</span>
              </button>
            </div>

            <!-- Loading状态 -->
            <div v-if="isLoadingStats" class="loading-stats">
              <div class="loading-spinner"></div>
              <div class="loading-text">Loading Statistics...</div>
            </div>

            <!-- 统计结果 -->
            <div v-else-if="statisticsData.length > 0" class="stats-results">
              <div class="stats-summary">
                <div class="summary-card">
                  <div class="summary-value">${{ totalSales.toFixed(2) }}</div>
                  <div class="summary-label">Total Sales</div>
                </div>
                <div class="summary-card">
                  <div class="summary-value">{{ totalOrders }}</div>
                  <div class="summary-label">Total Orders</div>
                </div>
                <div class="summary-card">
                  <div class="summary-value">${{ averageOrderValue.toFixed(2) }}</div>
                  <div class="summary-label">Avg Order Value</div>
                </div>
              </div>

              <div class="salesman-stats">
                <h4 class="stats-section-title">Salesman Performance</h4>
                <div class="stats-list">
                  <div
                    v-for="stat in statisticsData"
                    :key="stat.salesman"
                    class="stat-item"
                  >
                    <div class="stat-info">
                      <div class="stat-avatar">
                        <van-icon name="contact" color="#7ed321" size="20" />
                      </div>
                      <div class="stat-details">
                        <div class="stat-name">{{ stat.salesmanName }}</div>
                        <div class="stat-orders">{{ stat.orders }} orders</div>
                      </div>
                    </div>
                    <div class="stat-sales">
                      <div class="sales-amount">${{ stat.sales.toFixed(2) }}</div>
                      <div class="sales-percentage">{{ stat.percentage.toFixed(1) }}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else-if="hasQueried && !isLoadingStats" class="empty-stats">
              <van-icon name="chart-trending-o" color="#cccccc" size="48" />
              <h3>No Data Found</h3>
              <p>No sales data found for the selected date range</p>
            </div>
          </div>
        </van-tab>
        
        <van-tab title="Security" name="security">
          <div class="security-content">
            <div class="security-header">
              <van-icon name="shield-o" color="#7ed321" size="24" />
              <h3 class="security-title">Change Password</h3>
            </div>

            <div class="password-form">
              <div class="form-group">
                <label class="form-label">Current Password</label>
                <van-field
                  v-model="passwordForm.currentPassword"
                  type="password"
                  placeholder="Enter current password"
                  :border="false"
                  class="custom-field"
                />
              </div>

              <div class="form-group">
                <label class="form-label">New Password</label>
                <van-field
                  v-model="passwordForm.newPassword"
                  type="password"
                  placeholder="Enter new password"
                  :border="false"
                  class="custom-field"
                />
              </div>

              <div class="form-group">
                <label class="form-label">Confirm New Password</label>
                <van-field
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  placeholder="Confirm new password"
                  :border="false"
                  class="custom-field"
                />
              </div>

              <div class="form-actions">
                <button
                  class="save-btn"
                  :disabled="!isPasswordFormValid"
                  @click="changePassword"
                >
                  Change Password
                </button>
              </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <!-- Filter弹窗 -->
    <van-popup
      v-model:show="showFilterPopup"
      position="bottom"
      :style="{ height: '70vh' }"
      round
      closeable
      close-icon="cross"
      @close="closeFilterPopup"
    >
      <div class="filter-popup">
        <div class="filter-header">
          <h3 class="filter-title">Filter Orders</h3>
        </div>

        <div class="filter-content">
          <!-- 时间范围筛选 -->
          <div class="filter-section">
            <h4 class="filter-section-title">Date Range</h4>

            <!-- 自定义时间范围 - 横向布局 -->
            <div class="date-inputs">
              <div class="date-input-group">
                <label class="date-label">Start Date</label>
                <van-field
                  v-model="tempFilters.startDate"
                  readonly
                  placeholder="Select start date"
                  @click="showFilterStartDatePicker = true"
                  :border="false"
                  class="date-field"
                >
                  <template #right-icon>
                    <van-icon name="calendar-o" color="#7ed321" size="16" />
                  </template>
                </van-field>
              </div>

              <div class="date-input-group">
                <label class="date-label">End Date</label>
                <van-field
                  v-model="tempFilters.endDate"
                  readonly
                  placeholder="Select end date"
                  @click="showFilterEndDatePicker = true"
                  :border="false"
                  class="date-field"
                >
                  <template #right-icon>
                    <van-icon name="calendar-o" color="#7ed321" size="16" />
                  </template>
                </van-field>
              </div>
            </div>

            <!-- 快速选择选项 - 横向布局 -->
            <div class="date-range-quick-options">
              <div
                v-for="range in quickDateRangeOptions"
                :key="range.value"
                class="quick-date-option"
                @click="selectDateRange(range.value)"
              >
                <span>{{ range.label }}</span>
              </div>

              <!-- 清除时间选项 -->
              <div
                class="quick-date-option clear-date-option"
                @click="clearDateRange"
              >
                <van-icon name="delete-o" color="#e74c3c" size="12" />
                <span>Clear</span>
              </div>
            </div>
          </div>

          <!-- 订单状态筛选 -->
          <div class="filter-section">
            <h4 class="filter-section-title">Order Status</h4>
            <div class="status-options">
              <div
                v-for="status in statusOptions"
                :key="status.value"
                class="status-option"
                :class="{ active: tempFilters.status.includes(status.value) }"
                @click="toggleStatus(status.value)"
              >
                <div class="status-content">
                  <div class="status-indicator" :class="status.value.toLowerCase()"></div>
                  <span class="status-label">{{ status.label }}</span>
                </div>
                <div
                  v-if="tempFilters.status.includes(status.value)"
                  class="status-badge"
                >
                  <van-icon name="success" color="white" size="10" />
                </div>
              </div>
            </div>
          </div>

          <!-- 业务员筛选 -->
          <div class="filter-section">
            <h4 class="filter-section-title">Salesman</h4>
            <div class="salesman-options">
              <div
                v-for="salesman in salesmanOptions"
                :key="salesman.value"
                class="salesman-option"
                :class="{ active: tempFilters.salesman.includes(salesman.value) }"
                @click="toggleSalesman(salesman.value)"
              >
                <div class="salesman-left">
                  <van-icon name="contact" color="#7ed321" size="16" />
                  <span>{{ salesman.label }}</span>
                </div>
                <van-icon
                  v-if="tempFilters.salesman.includes(salesman.value)"
                  name="success"
                  color="#7ed321"
                  size="16"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="filter-footer">
          <button class="apply-btn" @click="applyFilters">
            Apply Filters ({{ filteredOrdersCount }})
          </button>
        </div>
      </div>
    </van-popup>

    <!-- 开始日期选择器 -->
    <van-popup v-model:show="showStartDatePicker" position="bottom" round>
      <van-date-picker
        v-model="startDateValue"
        title="Select Start Date"
        @confirm="confirmStartDate"
        @cancel="showStartDatePicker = false"
      />
    </van-popup>

    <!-- 结束日期选择器 -->
    <van-popup v-model:show="showEndDatePicker" position="bottom" round>
      <van-date-picker
        v-model="endDateValue"
        title="Select End Date"
        @confirm="confirmEndDate"
        @cancel="showEndDatePicker = false"
      />
    </van-popup>

    <!-- Filter开始日期选择器 -->
    <van-popup v-model:show="showFilterStartDatePicker" position="bottom" round>
      <van-date-picker
        v-model="filterStartDateValue"
        title="Select Start Date"
        @confirm="confirmFilterStartDate"
        @cancel="showFilterStartDatePicker = false"
      />
    </van-popup>

    <!-- Filter结束日期选择器 -->
    <van-popup v-model:show="showFilterEndDatePicker" position="bottom" round>
      <van-date-picker
        v-model="filterEndDateValue"
        title="Select End Date"
        @confirm="confirmFilterEndDate"
        @cancel="showFilterEndDatePicker = false"
      />
    </van-popup>

    <!-- 注销确认弹窗 -->
    <van-dialog
      v-model:show="showLogoutConfirm"
      title="Logout Confirmation"
      message="Are you sure you want to logout? You will be redirected to the login page."
      show-cancel-button
      confirm-button-text="Logout"
      cancel-button-text="Cancel"
      confirm-button-color="#e74c3c"
      @confirm="confirmLogout"
      @cancel="cancelLogout"
    />

    <!-- 底部TabBar -->
    <van-tabbar v-model="activeMainTab" class="custom-tabbar" @change="handleTabChange">
      <van-tabbar-item icon="home-o" name="home">
        Home
      </van-tabbar-item>
      <van-tabbar-item icon="user-o" name="profile">
        Profile
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog } from 'vant'
import api from '@/utils/api'

const router = useRouter()

// 响应式数据
const activeTab = ref('orders')
const activeMainTab = ref('profile')
const showFilterPopup = ref(false) // Filter弹窗显示状态
const showLogoutConfirm = ref(false) // 注销确认弹窗显示状态

// Filter相关数据
const filters = ref({
  dateRange: 'all',
  startDate: '',
  endDate: '',
  status: [],
  salesman: []
})

// 临时筛选条件（在弹窗中修改，点击Apply后应用到filters）
const tempFilters = ref({
  dateRange: 'all',
  startDate: '',
  endDate: '',
  status: [],
  salesman: []
})

// 密码表单数据
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// Statistics相关数据
const showStartDatePicker = ref(false)
const showEndDatePicker = ref(false)
const showFilterStartDatePicker = ref(false)
const showFilterEndDatePicker = ref(false)
const filterStartDateValue = ref(['2025', '06', '01'])
const filterEndDateValue = ref(['2025', '06', '25'])
const startDateValue = ref(['2025', '06', '01'])
const endDateValue = ref(['2025', '06', '25'])
const statsForm = ref({
  startDate: '',
  endDate: ''
})
const statisticsData = ref([])
const hasQueried = ref(false)
const isLoadingStats = ref(false)

// Filter选项数据
const quickDateRangeOptions = ref([
  { label: 'Last 7 Days', value: '7days' },
  { label: 'Last 30 Days', value: '30days' },
  { label: 'Last 3 Months', value: '3months' }
])

const statusOptions = ref([
  { label: 'Delivered', value: 'Delivered' },
  { label: 'Processing', value: 'Processing' },
  { label: 'Cancelled', value: 'Cancelled' }
])

const salesmanOptions = ref([
  { label: 'John Smith', value: 'john' },
  { label: 'Alice Johnson', value: 'alice' },
  { label: 'Bob Wilson', value: 'bob' },
  { label: 'Carol Brown', value: 'carol' }
])

// 用户信息
const userInfo = ref({
  name: 'John Smith',
  group: 'Sales Team A',
  role: 'Senior Sales Representative',
  avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=John&backgroundColor=7ed321'
})

// 模拟订单数据
const allOrdersList = ref([
  {
    id: 1,
    orderNumber: 'ORD-2024-001',
    date: '2025-06-24', // 昨天
    dateObj: new Date('2025-06-24'),
    status: 'Delivered',
    salesman: 'john',
    total: 156.99,
    items: [
      {
        id: 1,
        name: 'Fresh Organic Apples',
        quantity: 3,
        image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=60&h=60&fit=crop'
      },
      {
        id: 2,
        name: 'Premium Avocados',
        quantity: 2,
        image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=60&h=60&fit=crop'
      },
      {
        id: 3,
        name: 'Organic Bananas',
        quantity: 1,
        image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=60&h=60&fit=crop'
      }
    ]
  },
  {
    id: 2,
    orderNumber: 'ORD-2024-002',
    date: '2025-06-22', // 3天前
    dateObj: new Date('2025-06-22'),
    status: 'Processing',
    salesman: 'alice',
    total: 89.50,
    items: [
      {
        id: 4,
        name: 'Fresh Strawberries',
        quantity: 2,
        image: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=60&h=60&fit=crop'
      },
      {
        id: 5,
        name: 'Organic Bananas',
        quantity: 1,
        image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=60&h=60&fit=crop'
      }
    ]
  },
  {
    id: 3,
    orderNumber: 'ORD-2024-003',
    date: '2025-06-15', // 10天前
    dateObj: new Date('2025-06-15'),
    status: 'Cancelled',
    salesman: 'bob',
    total: 234.75,
    items: [
      {
        id: 6,
        name: 'Premium Avocados',
        quantity: 5,
        image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=60&h=60&fit=crop'
      }
    ]
  },
  {
    id: 4,
    orderNumber: 'ORD-2024-004',
    date: '2025-05-28', // 27天前
    dateObj: new Date('2025-05-28'),
    status: 'Delivered',
    salesman: 'carol',
    total: 78.25,
    items: [
      {
        id: 7,
        name: 'Fresh Strawberries',
        quantity: 1,
        image: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=60&h=60&fit=crop'
      }
    ]
  },
  {
    id: 5,
    orderNumber: 'ORD-2024-005',
    date: '2025-03-20', // 3个月前
    dateObj: new Date('2025-03-20'),
    status: 'Processing',
    salesman: 'john',
    total: 145.80,
    items: [
      {
        id: 8,
        name: 'Fresh Organic Apples',
        quantity: 2,
        image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=60&h=60&fit=crop'
      }
    ]
  }
])

// 计算属性
const ordersList = computed(() => {
  let filtered = allOrdersList.value

  // 按日期范围筛选
  if (filters.value.dateRange !== 'all') {
    if (filters.value.startDate && filters.value.endDate) {
      // 自定义日期范围
      const startDate = new Date(filters.value.startDate)
      const endDate = new Date(filters.value.endDate)
      endDate.setHours(23, 59, 59, 999) // 包含结束日期的整天

      filtered = filtered.filter(order => {
        return order.dateObj >= startDate && order.dateObj <= endDate
      })
    } else {
      // 预设日期范围
      const now = new Date()
      let cutoffDate

      switch (filters.value.dateRange) {
        case '7days':
          cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30days':
          cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '3months':
          cutoffDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
      }

      if (cutoffDate) {
        filtered = filtered.filter(order => order.dateObj >= cutoffDate)
      }
    }
  }

  // 按状态筛选
  if (filters.value.status.length > 0) {
    filtered = filtered.filter(order => filters.value.status.includes(order.status))
  }

  // 按业务员筛选
  if (filters.value.salesman.length > 0) {
    filtered = filtered.filter(order => filters.value.salesman.includes(order.salesman))
  }

  return filtered
})

const hasActiveFilters = computed(() => {
  return filters.value.dateRange !== 'all' ||
         filters.value.startDate !== '' ||
         filters.value.endDate !== '' ||
         filters.value.status.length > 0 ||
         filters.value.salesman.length > 0
})

const filteredOrdersCount = computed(() => {
  // 基于tempFilters计算预览数量
  let filtered = allOrdersList.value

  // 按日期范围筛选
  if (tempFilters.value.dateRange !== 'all') {
    if (tempFilters.value.startDate && tempFilters.value.endDate) {
      // 自定义日期范围
      const startDate = new Date(tempFilters.value.startDate)
      const endDate = new Date(tempFilters.value.endDate)
      endDate.setHours(23, 59, 59, 999) // 包含结束日期的整天

      filtered = filtered.filter(order => {
        return order.dateObj >= startDate && order.dateObj <= endDate
      })
    } else {
      // 预设日期范围
      const now = new Date()
      let cutoffDate

      switch (tempFilters.value.dateRange) {
        case '7days':
          cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30days':
          cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '3months':
          cutoffDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
      }

      if (cutoffDate) {
        filtered = filtered.filter(order => order.dateObj >= cutoffDate)
      }
    }
  }

  // 按状态筛选
  if (tempFilters.value.status.length > 0) {
    filtered = filtered.filter(order => tempFilters.value.status.includes(order.status))
  }

  // 按业务员筛选
  if (tempFilters.value.salesman.length > 0) {
    filtered = filtered.filter(order => tempFilters.value.salesman.includes(order.salesman))
  }

  return filtered.length
})

// 密码表单验证
const isPasswordFormValid = computed(() => {
  return passwordForm.value.currentPassword.length > 0 &&
         passwordForm.value.newPassword.length > 0 &&
         passwordForm.value.confirmPassword === passwordForm.value.newPassword
})

// Statistics计算属性
const isDateRangeValid = computed(() => {
  return statsForm.value.startDate && statsForm.value.endDate
})

const totalSales = computed(() => {
  return statisticsData.value.reduce((sum, stat) => sum + stat.sales, 0)
})

const totalOrders = computed(() => {
  return statisticsData.value.reduce((sum, stat) => sum + stat.orders, 0)
})

const averageOrderValue = computed(() => {
  return totalOrders.value > 0 ? totalSales.value / totalOrders.value : 0
})

// 处理订单点击
const handleOrderClick = (order) => {
  console.log('查看订单详情:', order.orderNumber)
  // 这里可以添加跳转到订单详情页面的逻辑
}

// Filter相关方法
const selectDateRange = (range) => {
  const now = new Date()
  let startDate, endDate

  switch (range) {
    case '7days':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      endDate = now
      break
    case '30days':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      endDate = now
      break
    case '3months':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      endDate = now
      break
    case 'all':
    default:
      tempFilters.value.startDate = ''
      tempFilters.value.endDate = ''
      tempFilters.value.dateRange = range
      return
  }

  // 格式化日期
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  tempFilters.value.startDate = formatDate(startDate)
  tempFilters.value.endDate = formatDate(endDate)
  tempFilters.value.dateRange = range
}

// 清除日期范围
const clearDateRange = () => {
  tempFilters.value.startDate = ''
  tempFilters.value.endDate = ''
  tempFilters.value.dateRange = 'all'
}

const toggleStatus = (status) => {
  const index = tempFilters.value.status.indexOf(status)
  if (index > -1) {
    tempFilters.value.status.splice(index, 1)
  } else {
    tempFilters.value.status.push(status)
  }
}

const toggleSalesman = (salesman) => {
  const index = tempFilters.value.salesman.indexOf(salesman)
  if (index > -1) {
    tempFilters.value.salesman.splice(index, 1)
  } else {
    tempFilters.value.salesman.push(salesman)
  }
}

const resetFilters = () => {
  filters.value = {
    dateRange: 'all',
    startDate: '',
    endDate: '',
    status: [],
    salesman: []
  }
  tempFilters.value = {
    dateRange: 'all',
    startDate: '',
    endDate: '',
    status: [],
    salesman: []
  }
}

const applyFilters = () => {
  // 将临时筛选条件应用到实际筛选条件
  filters.value = {
    dateRange: tempFilters.value.dateRange,
    startDate: tempFilters.value.startDate,
    endDate: tempFilters.value.endDate,
    status: [...tempFilters.value.status],
    salesman: [...tempFilters.value.salesman]
  }
  showFilterPopup.value = false
  console.log('应用筛选条件:', filters.value)
}

const openFilterPopup = () => {
  // 打开弹窗时初始化临时筛选条件为当前实际筛选条件
  tempFilters.value = {
    dateRange: filters.value.dateRange,
    startDate: filters.value.startDate,
    endDate: filters.value.endDate,
    status: [...filters.value.status],
    salesman: [...filters.value.salesman]
  }
  showFilterPopup.value = true
}

const closeFilterPopup = () => {
  // 关闭弹窗时恢复临时筛选条件为当前实际筛选条件
  tempFilters.value = {
    dateRange: filters.value.dateRange,
    startDate: filters.value.startDate,
    endDate: filters.value.endDate,
    status: [...filters.value.status],
    salesman: [...filters.value.salesman]
  }
  showFilterPopup.value = false
}

// 密码相关方法
const changePassword = async () => {
  if (!isPasswordFormValid.value) {
    return
  }

  try {
    // 这里可以添加实际的密码修改API调用
    console.log('修改密码:', {
      currentPassword: passwordForm.value.currentPassword,
      newPassword: passwordForm.value.newPassword
    })

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 显示成功消息
    alert('Password changed successfully!')

    // 重置表单
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }

  } catch (error) {
    console.error('密码修改失败:', error)
    alert('Failed to change password. Please try again.')
  }
}

// 注销相关方法
const showLogoutDialog = () => {
  showLogoutConfirm.value = true
}

const confirmLogout = () => {
  // 使用 API 清除登录缓存
  api.clearLoginCache()

  // 清空应用状态
  console.log('用户已注销，清空缓存')

  // 跳转到登录页
  router.push('/')
}

const cancelLogout = () => {
  showLogoutConfirm.value = false
  console.log('取消注销')
}

// Statistics相关方法
const formatDate = (dateArray) => {
  return `${dateArray[0]}-${dateArray[1].padStart(2, '0')}-${dateArray[2].padStart(2, '0')}`
}

const confirmStartDate = () => {
  statsForm.value.startDate = formatDate(startDateValue.value)
  showStartDatePicker.value = false
}

const confirmEndDate = () => {
  statsForm.value.endDate = formatDate(endDateValue.value)
  showEndDatePicker.value = false
}

// Filter日期选择器确认方法
const confirmFilterStartDate = () => {
  tempFilters.value.startDate = formatDate(filterStartDateValue.value)
  tempFilters.value.dateRange = 'custom'
  showFilterStartDatePicker.value = false
}

const confirmFilterEndDate = () => {
  tempFilters.value.endDate = formatDate(filterEndDateValue.value)
  tempFilters.value.dateRange = 'custom'
  showFilterEndDatePicker.value = false
}

// Statistics页面的快捷日期选择
const selectStatsDateRange = (range) => {
  const now = new Date()
  let startDate, endDate

  switch (range) {
    case '7days':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      endDate = now
      break
    case '30days':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      endDate = now
      break
    case '3months':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      endDate = now
      break
    default:
      return
  }

  // 格式化日期
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  statsForm.value.startDate = formatDate(startDate)
  statsForm.value.endDate = formatDate(endDate)
}

// 清除Statistics页面的日期范围
const clearStatsDateRange = () => {
  statsForm.value.startDate = ''
  statsForm.value.endDate = ''
}

const queryStatistics = async () => {
  if (!isDateRangeValid.value) return

  try {
    // 开始加载
    isLoadingStats.value = true

    // 模拟API调用
    console.log('查询统计数据:', {
      startDate: statsForm.value.startDate,
      endDate: statsForm.value.endDate
    })

    // 模拟数据
    const mockData = [
      {
        salesman: 'john',
        salesmanName: 'John Smith',
        sales: 15600.50,
        orders: 45,
        percentage: 35.2
      },
      {
        salesman: 'alice',
        salesmanName: 'Alice Johnson',
        sales: 12800.75,
        orders: 38,
        percentage: 28.9
      },
      {
        salesman: 'bob',
        salesmanName: 'Bob Wilson',
        sales: 9500.25,
        orders: 28,
        percentage: 21.4
      },
      {
        salesman: 'carol',
        salesmanName: 'Carol Brown',
        sales: 6400.80,
        orders: 19,
        percentage: 14.5
      }
    ]

    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    statisticsData.value = mockData
    hasQueried.value = true

  } catch (error) {
    console.error('查询统计数据失败:', error)
    statisticsData.value = []
    hasQueried.value = true
  } finally {
    // 结束加载
    isLoadingStats.value = false
  }
}

// 处理底部Tab切换
const handleTabChange = (name) => {
  if (name === 'home') {
    router.push('/home')
  }
}
</script>

<style scoped>
/* Profile页面容器 */
.profile-container {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: 60px; /* 为底部TabBar留出空间 */
}

/* 账号信息区域 */
.account-section {
  background: linear-gradient(135deg, #7ed321 0%, #6bc91a 100%);
  padding: 40px 20px 30px;
  color: white;
}

.account-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-container {
  position: relative;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
  display: block;
}

.online-status {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #2ecc71;
  border: 3px solid white;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: white;
}

.user-group {
  font-size: 14px;
  margin: 0 0 2px 0;
  color: rgba(255, 255, 255, 0.9);
}

.user-role {
  font-size: 12px;
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.logout-btn:active {
  transform: translateY(0);
}

.logout-text {
  font-size: 12px;
  font-weight: 500;
  color: white;
  white-space: nowrap;
}

/* Tab区域 */
.tabs-section {
  background: white;
  margin-top: -10px;
  border-radius: 16px 16px 0 0;
  min-height: calc(100vh - 200px);
}

.custom-tabs {
  --van-tabs-line-height: 44px;
  --van-tabs-card-height: 44px;
}

:deep(.van-tabs__wrap) {
  padding: 0 20px;
  background: white;
  border-radius: 16px 16px 0 0;
}

:deep(.van-tabs__nav) {
  background: white;
}

:deep(.van-tab) {
  font-weight: 500;
}

/* 订单内容 */
.orders-content {
  padding: 20px;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.orders-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reset-filters-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #e74c3c 100%);
  border: none;
  color: white;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
  position: relative;
  overflow: hidden;
}

.reset-filters-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
  background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
}

.reset-filters-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
}

.filter-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #7ed321;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  height: 32px;
}

.filter-btn:hover {
  background-color: rgba(126, 211, 33, 0.1);
}

.filter-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #e74c3c;
  border-radius: 50%;
  border: 2px solid white;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #7ed321;
}

.order-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #6bc91a;
}



.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.order-number {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.order-date {
  font-size: 12px;
  color: #666666;
}

.order-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.order-status.delivered {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.order-status.processing {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.order-status.cancelled {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

/* 订单商品 */
.order-items {
  margin-bottom: 12px;
}

.order-product {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.product-thumb {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.product-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-name {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

.product-quantity {
  font-size: 12px;
  color: #666666;
}

.more-items {
  font-size: 12px;
  color: #7ed321;
  font-weight: 500;
  margin-left: 52px;
}

/* 订单底部 */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.order-total {
  display: flex;
  align-items: center;
}

.total-amount {
  font-size: 16px;
  font-weight: 700;
  color: #7ed321;
}

.order-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.view-btn {
  background: rgba(126, 211, 33, 0.1);
  color: #7ed321;
}

.view-btn:hover {
  background: rgba(126, 211, 33, 0.2);
}

.reorder-btn {
  background: #7ed321;
  color: white;
}

.reorder-btn:hover {
  background: #6bc91a;
}

/* Statistics页面样式 */
.stats-content {
  padding: 20px;
}

.stats-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.stats-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

/* 日期选择器 */
.date-selector {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #7ed321;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.date-selector:hover {
  border-color: #6bc91a;
  box-shadow: 0 4px 16px rgba(126, 211, 33, 0.15);
}

.date-inputs {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1; /* 确保两个输入组等宽 */
}

.date-label {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
}

.date-field {
  --van-field-input-height: 44px;
  --van-field-background: #f8f9fa;
  --van-field-border-radius: 6px;
  --van-field-input-text-color: #1a1a1a;
  --van-field-placeholder-text-color: #999999;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.date-field:focus-within {
  border-color: #7ed321;
  box-shadow: 0 0 0 2px rgba(126, 211, 33, 0.1);
}

.query-btn {
  width: 100%;
  height: 48px;
  background: #7ed321;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(126, 211, 33, 0.2);
}

.query-btn:hover:not(:disabled) {
  background: #6bc91a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(126, 211, 33, 0.3);
}

.query-btn:disabled {
  background: #e5e5e5;
  color: #999999;
  cursor: not-allowed;
  box-shadow: none;
}

/* Statistics页面快速日期选择选项 */
.stats-date-range-quick-options {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
}

.stats-quick-date-option {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #e5e5e5;
  border-radius: 16px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
  text-align: center;
  color: #666666;
}

.stats-quick-date-option:hover {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
  color: #7ed321;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(126, 211, 33, 0.2);
}

.stats-quick-date-option:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(126, 211, 33, 0.3);
}

/* Statistics页面清除日期选项 - 红色主题 */
.stats-quick-date-option.stats-clear-date-option {
  border-color: #e74c3c;
  background: rgba(231, 76, 60, 0.05);
  color: #e74c3c;
  gap: 4px;
}

.stats-quick-date-option.stats-clear-date-option:hover {
  border-color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
}

.stats-quick-date-option.stats-clear-date-option:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(231, 76, 60, 0.3);
}

/* 统计结果 */
.stats-results {
  margin-top: 24px;
}

.stats-summary {
  display: flex;
  flex-direction: row;
  gap: 12px;
  margin-bottom: 24px;
  overflow-x: auto;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  border: 1px solid #7ed321;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  flex: 1;
  min-width: 120px;
}



.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: #7ed321;
  margin-bottom: 6px;
  white-space: nowrap;
}

.summary-label {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
  white-space: nowrap;
}

/* 业务员统计 */
.salesman-stats {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #7ed321;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.salesman-stats:hover {
  border-color: #6bc91a;
  box-shadow: 0 4px 16px rgba(126, 211, 33, 0.15);
}

.stats-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0; /* 移除gap，使用margin-bottom控制间距 */
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-item:hover {
  background: rgba(126, 211, 33, 0.02);
  border-color: rgba(126, 211, 33, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(126, 211, 33, 0.1);
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(126, 211, 33, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.stat-orders {
  font-size: 12px;
  color: #666666;
}

.stat-sales {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.sales-amount {
  font-size: 16px;
  font-weight: 700;
  color: #7ed321;
}

.sales-percentage {
  font-size: 12px;
  color: #666666;
}

/* Loading状态 */
.loading-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  margin-top: 24px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e5e5;
  border-top: 4px solid #7ed321;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 16px;
  color: #7ed321;
  font-weight: 500;
}

/* 空状态 */
.empty-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
}

.empty-stats h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 16px 0 8px 0;
}

.empty-stats p {
  font-size: 14px;
  color: #666666;
  margin: 0;
}

/* Security页面样式 */
.security-content {
  padding: 20px;
}

.security-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.security-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.password-form {
  max-width: 400px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.custom-field {
  --van-field-input-height: 44px;
  --van-field-background: #f8f9fa;
  --van-field-border-radius: 8px;
  --van-field-input-text-color: #1a1a1a;
  --van-field-placeholder-text-color: #999999;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  transition: border-color 0.3s ease;
}

:deep(.custom-field .van-field__control) {
  padding: 0 16px;
}

.custom-field:focus-within {
  border-color: #7ed321;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.save-btn {
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 160px;
  background: #7ed321;
  color: white;
  box-shadow: 0 2px 8px rgba(126, 211, 33, 0.3);
}

.save-btn:hover:not(:disabled) {
  background: #6bc91a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(126, 211, 33, 0.4);
}

.save-btn:disabled {
  background: #cccccc;
  color: #999999;
  cursor: not-allowed;
  box-shadow: none;
}

/* 底部TabBar */
.custom-tabbar {
  --van-tabbar-background: #ffffff;
  --van-tabbar-item-text-color: #999999;
  --van-tabbar-item-active-color: #7ed321;
  --van-tabbar-item-active-background: transparent;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.van-tabbar-item__icon) {
  font-size: 20px;
}

:deep(.van-tabbar-item__text) {
  font-size: 12px;
  font-weight: 500;
}

/* Filter弹窗样式 */
.filter-popup {
  background: #ffffff;
  border-radius: 16px 16px 0 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 16px 60px 16px 20px; /* 右侧留出关闭按钮空间 */
  border-bottom: 1px solid #e5e5e5;
  height: 52px; /* 固定高度 */
  flex-shrink: 0;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 16px;
}

.filter-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

/* 快速日期选择选项 - 横向布局 */
.date-range-quick-options {
  display: flex;
  gap: 8px;
}

.quick-date-option {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #e5e5e5;
  border-radius: 16px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
  text-align: center;
  color: #666666;
}

.quick-date-option:hover {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
  color: #7ed321;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(126, 211, 33, 0.2);
}

.quick-date-option:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(126, 211, 33, 0.3);
}

/* 清除日期选项 - 红色主题 */
.quick-date-option.clear-date-option {
  border-color: #e74c3c;
  background: rgba(231, 76, 60, 0.05);
  color: #e74c3c;
  gap: 4px;
}

.quick-date-option.clear-date-option:hover {
  border-color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
}

.quick-date-option.clear-date-option:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(231, 76, 60, 0.3);
}



/* 状态选项 */
.status-options {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.status-option {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 12px;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
  flex: 1; /* 确保每个选项等宽 */
}

.status-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  color: #666666;
}

.status-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: #7ed321;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-option:hover {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.05);
}

.status-option.active {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
}

.status-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-indicator.delivered {
  background: #2ecc71;
}

.status-indicator.processing {
  background: #3498db;
}

.status-indicator.cancelled {
  background: #e74c3c;
}

/* 业务员选项 */
.salesman-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.salesman-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px;
}

.salesman-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.salesman-option:hover {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.05);
}

.salesman-option.active {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
}

/* Filter底部 */
.filter-footer {
  padding: 16px;
  border-top: 1px solid #e5e5e5;
  background: white;
}

.apply-btn {
  width: 100%;
  height: 48px;
  background: #7ed321;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  background: #6bc91a;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .account-section {
    padding: 30px 16px 20px;
  }

  .avatar-container {
    width: 60px;
    height: 60px;
  }

  .user-name {
    font-size: 18px;
  }

  .orders-content {
    padding: 16px;
  }

  .order-item {
    padding: 12px;
  }

  .date-range-quick-options {
    gap: 6px;
  }

  .quick-date-option {
    min-height: 36px;
    font-size: 11px;
    padding: 8px 8px;
  }

  .status-options {
    gap: 6px; /* 移动端减小间距 */
  }

  .status-option {
    min-height: 60px;
    padding: 12px 6px; /* 移动端减小内边距 */
  }

  .status-label {
    font-size: 11px;
  }

  .security-content {
    padding: 16px;
  }

  .password-form {
    max-width: none;
  }

  .save-btn {
    width: 100%;
    min-width: auto;
  }

  .stats-content {
    padding: 16px;
  }

  .date-inputs {
    gap: 12px; /* 移动端减小间距 */
  }

  .stats-date-range-quick-options {
    gap: 6px;
  }

  .stats-quick-date-option {
    min-height: 36px;
    font-size: 11px;
    padding: 8px 8px;
  }

  .stats-summary {
    flex-direction: row;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 4px;
  }

  .summary-card {
    padding: 12px;
    min-width: 100px;
    flex-shrink: 0;
  }

  .summary-value {
    font-size: 16px;
  }

  .summary-label {
    font-size: 11px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-avatar {
    width: 36px;
    height: 36px;
  }

  .sales-amount {
    font-size: 14px;
  }
}
</style>
