import axios from 'axios'
import { showFailToast } from 'vant'

/**
 * API 接口管理类
 * 基于 axios 封装，提供统一的请求配置和拦截器
 */
class ApiClient {
  constructor(config = {}) {
    // 默认配置
    const defaultConfig = {
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://206.233.245.198/LoadData',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    }

    // 合并配置
    this.config = { ...defaultConfig, ...config }
    
    // 创建 axios 实例
    this.instance = axios.create(this.config)
    
    // 设置拦截器
    this.setupInterceptors()
  }

  /**
   * 设置请求和响应拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        console.log('🚀 发送请求:', config.method?.toUpperCase(), config.url)

        // 添加认证 token
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        return config
      },
      (error) => {
        console.error('❌ 请求错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        console.log('✅ 响应成功:', response.config.url, response.status)

        const { data } = response

        // 处理服务器返回的标准格式: { code, msg, data }
        if (data && typeof data === 'object' && 'code' in data) {
          if (data.code === 0) {
            // 成功响应（code: 0 表示成功），返回 data 字段
            return data.data || data
          } else {
            // 业务错误，抛出错误并附带 code 信息
            const errorMessage = data.msg || '请求失败'
            console.error('❌ 业务错误:', data.code, errorMessage)

            // 创建错误对象，包含 code 和 message
            const error = new Error(errorMessage)
            error.code = data.code
            error.serverResponse = data
            throw error
          }
        }

        // 如果不是标准格式，直接返回原数据
        return data
      },
      (error) => {
        console.error('❌ 响应错误:', error)
        this.handleError(error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 处理错误响应
   */
  handleError(error) {
    let errorMessage = '网络请求失败'
    let shouldRedirectToLogin = false

    // 检查是否是业务逻辑错误（从响应拦截器抛出的错误）
    if (error.code && error.serverResponse) {
      const { code, msg } = error.serverResponse
      errorMessage = msg || '请求失败'

      // 根据业务错误码进行特殊处理
      switch (code) {
        case 1:
          // 登录失败，账号或密码错误
          console.log('登录失败:', errorMessage)
          break
        case 403:
          // 需要登录
          shouldRedirectToLogin = true
          break
        default:
          console.log('其他业务错误:', code, errorMessage)
      }
    } else if (error.response) {
      // HTTP 错误
      const { status, data } = error.response

      // 优先使用服务器返回的 msg 字段
      if (data && data.msg) {
        errorMessage = data.msg

        // 检查业务错误码
        if (data.code === 403) {
          shouldRedirectToLogin = true
        }
      } else {
        // 根据 HTTP 状态码处理
        switch (status) {
          case 400:
            errorMessage = '请求参数错误'
            break
          case 401:
            errorMessage = '未授权，请重新登录'
            shouldRedirectToLogin = true
            break
          case 403:
            errorMessage = '拒绝访问'
            shouldRedirectToLogin = true
            break
          case 404:
            errorMessage = '请求的资源不存在'
            break
          case 500:
            errorMessage = '服务器内部错误'
            break
          default:
            errorMessage = `请求失败 (${status})`
        }
      }
    } else if (error.request) {
      errorMessage = '网络连接超时，请检查网络'
    } else {
      errorMessage = error.message || '请求配置错误'
    }

    // 使用 Vant 4 Toast 显示错误信息
    showFailToast({
      message: errorMessage,
      duration: 3000
    })

    // 如果需要跳转到登录页
    if (shouldRedirectToLogin) {
      this.handleUnauthorized()
    }
  }

  /**
   * 处理未授权情况
   */
  handleUnauthorized() {
    // 清除登录缓存
    this.clearLoginCache()

    // 跳转到登录页面
    if (typeof window !== 'undefined' && window.location) {
      window.location.href = '/'
    }
  }

  /**
   * 获取存储的 token
   */
  getToken() {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token')
    }
    return null
  }

  /**
   * 设置 token
   */
  setToken(token) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token)
    }
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    if (typeof window !== 'undefined') {
      const userInfo = localStorage.getItem('user_info')
      return userInfo ? JSON.parse(userInfo) : null
    }
    return null
  }

  /**
   * 设置用户信息
   */
  setUserInfo(userInfo) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user_info', JSON.stringify(userInfo))
    }
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!this.getToken()
  }

  /**
   * 清除登录缓存
   */
  clearLoginCache() {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_info')
    }
  }

  // HTTP 方法封装
  get(url, params = {}, config = {}) {
    return this.instance.get(url, { params, ...config })
  }

  post(url, data = {}, config = {}) {
    return this.instance.post(url, data, config)
  }

  put(url, data = {}, config = {}) {
    return this.instance.put(url, data, config)
  }

  delete(url, config = {}) {
    return this.instance.delete(url, config)
  }

  // ========== 业务接口 ==========

  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名 (必填)
   * @param {string} loginData.password - 密码 (必填)
   * @returns {Promise} 登录结果
   */
  login(loginData) {
    return this.post('/login', loginData)
  }
}

// 创建默认实例
const api = new ApiClient()

// 导出类和默认实例
export { ApiClient }
export default api
