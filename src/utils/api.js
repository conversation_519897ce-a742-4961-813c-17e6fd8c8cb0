import axios from 'axios'

/**
 * API 接口管理类
 * 基于 axios 封装，提供统一的请求配置和拦截器
 */
class ApiClient {
  constructor(config = {}) {
    // 默认配置
    const defaultConfig = {
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://206.233.245.198/LoadData',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    }

    // 合并配置
    this.config = { ...defaultConfig, ...config }
    
    // 创建 axios 实例
    this.instance = axios.create(this.config)
    
    // 设置拦截器
    this.setupInterceptors()
  }

  /**
   * 设置请求和响应拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        console.log('🚀 发送请求:', config.method?.toUpperCase(), config.url)
        return config
      },
      (error) => {
        console.error('❌ 请求错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        console.log('✅ 响应成功:', response.config.url, response.status)
        return response.data
      },
      (error) => {
        console.error('❌ 响应错误:', error)
        this.handleError(error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 处理错误响应
   */
  handleError(error) {
    let errorMessage = '网络请求失败'

    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          errorMessage = data?.message || '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          this.handleUnauthorized()
          break
        case 403:
          errorMessage = '拒绝访问'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data?.message || \`请求失败 (\${status})\`
      }
    } else if (error.request) {
      errorMessage = '网络连接超时，请检查网络'
    } else {
      errorMessage = error.message || '请求配置错误'
    }

    alert(errorMessage)
  }

  /**
   * 处理未授权情况
   */
  handleUnauthorized() {
    // 跳转到登录页面
    if (typeof window !== 'undefined' && window.location) {
      window.location.href = '/'
    }
  }

  // HTTP 方法封装
  get(url, params = {}, config = {}) {
    return this.instance.get(url, { params, ...config })
  }

  post(url, data = {}, config = {}) {
    return this.instance.post(url, data, config)
  }

  put(url, data = {}, config = {}) {
    return this.instance.put(url, data, config)
  }

  delete(url, config = {}) {
    return this.instance.delete(url, config)
  }

  // ========== 业务接口 ==========

  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名 (必填)
   * @param {string} loginData.password - 密码 (必填)
   * @returns {Promise} 登录结果
   */
  login(loginData) {
    return this.post('/userLogin', loginData)
  }
}

// 创建默认实例
const api = new ApiClient()

// 导出类和默认实例
export { ApiClient }
export default api
