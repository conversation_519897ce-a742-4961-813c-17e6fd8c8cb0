/**
 * 登录状态管理测试文件
 * 用于验证登录、缓存、路由守卫等功能
 */

import api from './api'

// 测试登录状态管理
export const testAuthFlow = () => {
  console.log('=== 登录状态管理测试 ===')
  
  // 1. 检查初始状态
  console.log('1. 初始登录状态:', api.isLoggedIn())
  console.log('   Token:', api.getToken())
  console.log('   用户信息:', api.getUserInfo())
  
  // 2. 模拟登录成功
  console.log('\n2. 模拟登录成功')
  const mockLoginData = {
    token: 'mock-token-12345',
    userInfo: {
      id: 1,
      username: 'testuser',
      name: '测试用户',
      email: '<EMAIL>'
    }
  }
  
  api.setToken(mockLoginData.token)
  api.setUserInfo(mockLoginData.userInfo)
  
  console.log('   登录后状态:', api.isLoggedIn())
  console.log('   Token:', api.getToken())
  console.log('   用户信息:', api.getUserInfo())
  
  // 3. 测试清除缓存
  console.log('\n3. 测试清除缓存')
  api.clearLoginCache()
  
  console.log('   清除后状态:', api.isLoggedIn())
  console.log('   Token:', api.getToken())
  console.log('   用户信息:', api.getUserInfo())
  
  console.log('=== 测试完成 ===')
}

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  window.testAuthFlow = testAuthFlow
  console.log('💡 在控制台中输入 testAuthFlow() 来测试登录状态管理')
}
